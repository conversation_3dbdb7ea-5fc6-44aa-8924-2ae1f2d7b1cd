# CSS 重構清理指南

## 🧹 需要清理的文件

### 1. 已重構完成，可以移除的文件：
```bash
# 原始的 configLayout.css（已拆分為模組）
src/styles/pages/configLayout.css

# 如果不再需要，也可以考慮移除：
src/components/Tabs/ConfigSubTabs.module.css  # 已移動到新位置
```

### 2. 檢查並更新引用：

#### 檢查是否還有元件引用舊的 CSS 文件：
```bash
# 搜尋專案中是否還有引用 configLayout.css
grep -r "configLayout.css" src/
grep -r "ConfigSubTabs.module.css" src/

# 搜尋舊的類名使用
grep -r "edit-structure" src/ --include="*.tsx" --include="*.ts"
grep -r "structure-tag-icon" src/ --include="*.tsx" --include="*.ts"
grep -r "filter-select" src/ --include="*.tsx" --include="*.ts"
```

## 🔄 遷移檢查清單

### 已完成的重構：
- [x] 創建 `config-form.module.css`
- [x] 創建 `edit-structure.module.css`
- [x] 創建 `structure-tag.module.css`
- [x] 創建 `filter-select.module.css`
- [x] 創建統一入口 `index.module.css`
- [x] 移除 `main.tsx` 中的 `configLayout.css` 引用

### 需要手動完成的步驟：

#### 1. 更新使用這些樣式的元件：
```typescript
// 需要更新的元件可能包括：
// - EditStructureComponent
// - StructureTagComponent  
// - FilterSelectComponent
// - ConfigFormComponent

// 舊方式：
<div className="edit-structure">
  <nav className="edit-structure-navbar">

// 新方式：
import styles from '@/styles/components/structure/edit-structure.module.css'
<div className={styles.editStructure}>
  <nav className={styles.editStructureNavbar}>
```

#### 2. 檢查 Antd 元件樣式覆蓋：
確保所有使用 `:global()` 的樣式仍然正確工作：
```css
/* 檢查這些樣式是否正常 */
.filterSelect :global(.ant-select.ant-tree-select) { }
.formItemControl :global(.ant-form-item) { }
```

#### 3. 驗證圖片路徑：
檢查所有圖片引用是否正確：
```css
/* 確認這些路徑正確 */
background-image: url("../../../assets/icons/ok-circle-2.svg");
background-image: url("../../../assets/icons/filter.svg");
```

## 🧪 測試步驟

### 1. 視覺回歸測試：
- [ ] 配置頁面佈局正確
- [ ] 結構編輯器樣式正常
- [ ] 過濾選擇器功能正常
- [ ] 結構標籤互動正常
- [ ] 表單樣式正確

### 2. 功能測試：
- [ ] 所有懸停效果正常
- [ ] 動畫過渡效果正常
- [ ] 響應式佈局正常
- [ ] Antd 元件樣式覆蓋正常

### 3. 性能測試：
- [ ] CSS 文件大小合理
- [ ] 載入時間無明顯增加
- [ ] 無重複樣式載入

## 🚀 完成後的優勢

### 1. 更好的組織性：
- 樣式按功能模組分類
- 每個模組職責單一
- 便於維護和擴展

### 2. 更好的可重用性：
- 可以單獨引入需要的樣式模組
- 避免載入不必要的樣式
- 支援按需載入

### 3. 更好的類型安全：
- CSS Modules 提供類名的 TypeScript 支援
- 編譯時檢查類名是否存在
- 避免類名拼寫錯誤

### 4. 更好的作用域隔離：
- 避免全局樣式污染
- 每個模組的樣式獨立
- 減少樣式衝突風險

## 📞 如需協助

如果在遷移過程中遇到問題：
1. 檢查瀏覽器開發者工具中的樣式是否正確載入
2. 確認類名映射是否正確
3. 驗證 CSS 變數是否可用
4. 檢查圖片資源路徑是否正確
