# Antd 樣式覆蓋指南

## 🚨 問題描述

儘管 Antd 官方文檔聲稱使用 `:where()` 來降低樣式優先級，但在實際開發中，我們發現 Antd v5.20+ 的樣式優先級仍然很高，導致自定義樣式被覆蓋。

### 實際觀察到的問題：
```css
/* Antd 實際生成的樣式具有高優先級 */
:where(.css-dev-only-do-not-override-pi87um).ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active {
  background: var(--color-primary);
  border-radius: 6px 6px 0 0;
}
```

## 🛠️ 解決方案

### 方案一：雙重類名 + !important（推薦）

```css
/* 使用雙重類名提高優先級 */
.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-tab.ant-tabs-tab-active) {
  background: rgba(48, 48, 48, 0.4) !important;
  backdrop-filter: blur(2px) saturate(180%) !important;
  border: transparent !important;
}
```

**優點：**
- ✅ 確保樣式優先級
- ✅ 與 CSS Modules 兼容
- ✅ 不需要額外配置

**缺點：**
- ❌ 需要使用 !important
- ❌ 選擇器較長

### 方案二：CSS 層級（CSS Layers）

```css
@layer antd-override {
  .configSubTabs :global(.ant-tabs-tab-active) {
    background: rgba(48, 48, 48, 0.4);
  }
}
```

**優點：**
- ✅ 現代 CSS 解決方案
- ✅ 不需要 !important
- ✅ 更好的樣式組織

**缺點：**
- ❌ 瀏覽器支持度要求較高
- ❌ 需要額外的層級管理

### 方案三：ConfigProvider 主題定制

```typescript
<ConfigProvider theme={{
  components: {
    Tabs: {
      // 直接在主題中覆蓋樣式
      itemActiveColor: 'var(--color-gray_0)',
      itemSelectedColor: 'var(--color-gray_0)',
      cardBg: 'rgba(48, 48, 48, 0.4)',
    },
  },
}}>
```

**優點：**
- ✅ 官方推薦方式
- ✅ 類型安全
- ✅ 不會被覆蓋

**缺點：**
- ❌ 有些樣式無法通過主題定制
- ❌ 複雜樣式效果難以實現

## 🎯 最佳實踐

### 1. 優先使用 ConfigProvider
對於簡單的顏色、間距等樣式，優先使用 ConfigProvider 主題定制。

### 2. 複雜樣式使用雙重類名
對於複雜的視覺效果（如玻璃擬態、動畫等），使用雙重類名 + !important。

### 3. 組合使用
```typescript
// 結合 ConfigProvider 和 CSS Modules
<ConfigProvider theme={{ /* 基礎主題 */ }}>
  <div className={styles.configSubTabs}>
    <Tabs {...props} />
  </div>
</ConfigProvider>
```

## 🔧 實施步驟

### Step 1: 檢查樣式優先級
使用瀏覽器開發者工具檢查 Antd 樣式的實際優先級。

### Step 2: 選擇合適的覆蓋策略
- 簡單樣式 → ConfigProvider
- 複雜樣式 → CSS Modules + 雙重類名

### Step 3: 測試兼容性
確保樣式在不同瀏覽器和 Antd 版本中正常工作。

## 📝 注意事項

1. **避免過度使用 !important**：只在必要時使用
2. **保持選擇器簡潔**：避免過於複雜的選擇器
3. **版本兼容性**：注意 Antd 版本更新可能帶來的樣式變化
4. **性能考量**：複雜選擇器可能影響渲染性能

## 🚀 未來規劃

考慮遷移到 Emotion 或 Styled Components，這些 CSS-in-JS 解決方案能更好地處理樣式優先級問題：

```typescript
// Emotion 解決方案
const StyledTabs = styled(Tabs)`
  .ant-tabs-tab-active {
    background: rgba(48, 48, 48, 0.4);
    backdrop-filter: blur(2px) saturate(180%);
  }
`
```

這種方式能夠：
- ✅ 自動處理樣式優先級
- ✅ 提供更好的開發體驗
- ✅ 支持動態樣式
- ✅ 與 Antd 完美兼容
