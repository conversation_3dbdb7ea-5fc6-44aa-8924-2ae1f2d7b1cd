# CSS 架構重構計劃

## 🎯 目標
解決 CSS Modules 嵌套選擇器問題，建立與 Antd v5.20+ 完美兼容的樣式系統

## 📋 階段一：環境準備（預計 1-2 天）

### 1.1 安裝依賴
```bash
npm install @emotion/react @emotion/styled
npm install -D @emotion/babel-plugin @types/react
```

### 1.2 配置 Vite
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [
    react({
      jsxImportSource: '@emotion/react',
      babel: {
        plugins: ['@emotion/babel-plugin'],
      },
    }),
  ],
  // ... 其他配置
})
```

### 1.3 TypeScript 配置
```json
// tsconfig.json
{
  "compilerOptions": {
    "jsxImportSource": "@emotion/react",
    "types": ["@emotion/react/types/css-prop"]
  }
}
```

## 📋 階段二：設計系統建立（預計 2-3 天）

### 2.1 主題系統重構
- 將 CSS 變數遷移到 Emotion 主題
- 建立類型安全的主題接口
- 統一顏色、間距、字體系統

### 2.2 元件樣式庫建立
- 建立基礎樣式元件
- 定義設計 tokens
- 建立樣式工具函數

## 📋 階段三：元件遷移（預計 1-2 週）

### 3.1 優先級排序
1. **高頻元件**：Tabs, Modal, Table, Form
2. **佈局元件**：Layout, Header, Sidebar
3. **功能元件**：其他業務元件

### 3.2 遷移策略
- 保持原有 API 不變
- 漸進式替換
- 完整的測試覆蓋

## 📋 階段四：優化與清理（預計 3-5 天）

### 4.1 性能優化
- 樣式提取和優化
- 減少運行時開銷
- Bundle 大小分析

### 4.2 文檔和規範
- 樣式指南文檔
- 最佳實踐規範
- 團隊培訓材料

## 🔧 具體實施步驟

### Step 1: 立即解決當前問題
使用提供的 CSS Modules `:global()` 方案或 Emotion 混合方案

### Step 2: 建立新的樣式系統
```typescript
// src/styles/theme.ts
export const theme = {
  colors: {
    primary: {
      lightest: '#54ccd1',
      light: '#1fb5b5',
      default: '#00b3b9',
      dark: '#0a888c',
    },
    gray: {
      0: '#ffffff',
      1: '#c0c0c0',
      2: '#434a4f',
      3: '#363d42',
      4: '#2e3438',
      5: '#282e32',
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
  },
  breakpoints: {
    mobile: '480px',
    tablet: '768px',
    desktop: '1024px',
  },
}
```

### Step 3: 建立樣式工具
```typescript
// src/styles/utils.ts
import { css } from '@emotion/react'
import { theme } from './theme'

export const flexCenter = css`
  display: flex;
  justify-content: center;
  align-items: center;
`

export const glassMorphism = css`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
`

export const responsive = {
  mobile: (styles: any) => css`
    @media (max-width: ${theme.breakpoints.mobile}) {
      ${styles}
    }
  `,
  tablet: (styles: any) => css`
    @media (max-width: ${theme.breakpoints.tablet}) {
      ${styles}
    }
  `,
}
```

## 📊 風險評估與應對

### 風險等級：中等
- **技術風險**：學習曲線，但有完整文檔支持
- **時間風險**：可能需要額外 1-2 週適應期
- **兼容性風險**：Emotion 與 Antd 完美兼容

### 應對策略
1. **漸進式遷移**：不影響現有功能
2. **完整測試**：每個階段都有測試驗證
3. **回退方案**：保留原有 CSS 文件作為備份
4. **團隊培訓**：提供 Emotion 使用指南

## 📈 預期收益

### 短期收益（1-2 週）
- ✅ 解決 CSS Modules 嵌套選擇器問題
- ✅ 提升開發體驗
- ✅ 減少樣式衝突

### 長期收益（1-3 個月）
- ✅ 建立類型安全的樣式系統
- ✅ 提升代碼維護性
- ✅ 支持主題切換和動態樣式
- ✅ 減少 CSS 文件大小
- ✅ 提升團隊開發效率

## 🚀 下一步行動

1. **立即行動**：選擇並實施臨時解決方案
2. **本週內**：完成環境準備和依賴安裝
3. **下週開始**：啟動設計系統建立
4. **兩週後**：開始元件遷移工作

## 📞 支持與協助

如需具體實施指導或遇到技術問題，可以：
1. 參考提供的代碼示例
2. 查閱 Emotion 官方文檔
3. 尋求團隊技術支持
