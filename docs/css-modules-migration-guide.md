# CSS Modules 重構指南

## 🎯 重構概述

將 `configLayout.css` 重構為模組化的 CSS Modules，提升代碼組織性和維護性。

## 📁 新的文件結構

```
src/styles/components/
├── config/
│   └── index.module.css          # 統一入口
├── form/
│   └── config-form.module.css    # 表單相關樣式
├── structure/
│   ├── edit-structure.module.css # 結構編輯樣式
│   └── structure-tag.module.css  # 結構標籤樣式
├── select/
│   └── filter-select.module.css  # 過濾選擇器樣式
└── tabs/
    └── config-sub-tabs.module.css # 標籤頁樣式
```

## 🔧 使用方式

### 1. 單一模組使用

```typescript
// 只需要表單樣式
import formStyles from '@/styles/components/form/config-form.module.css'

function ConfigForm() {
  return (
    <div className={formStyles.formItemControl}>
      <div className={formStyles.noCheckboxBorder}>
        {/* 表單內容 */}
      </div>
    </div>
  )
}
```

### 2. 多模組組合使用

```typescript
// 需要多個模組樣式
import formStyles from '@/styles/components/form/config-form.module.css'
import structureStyles from '@/styles/components/structure/edit-structure.module.css'

function ConfigPage() {
  return (
    <div className={structureStyles.editStructure}>
      <nav className={structureStyles.editStructureNavbar}>
        {/* 導航內容 */}
      </nav>
      <main className={structureStyles.editStructureMain}>
        <div className={formStyles.formItemControl}>
          {/* 表單內容 */}
        </div>
      </main>
    </div>
  )
}
```

### 3. 統一入口使用

```typescript
// 使用統一入口（適合複雜頁面）
import configStyles from '@/styles/components/config/index.module.css'

function ComplexConfigPage() {
  // 可以使用所有模組的樣式
  return (
    <div>
      {/* 所有配置相關樣式都可用 */}
    </div>
  )
}
```

## 📋 樣式模組說明

### 1. config-form.module.css
- **用途**: 配置表單相關樣式
- **主要類名**:
  - `.formItemControl`: 表單項目控制樣式
  - `.noCheckboxBorder`: 無邊框複選框樣式

### 2. edit-structure.module.css
- **用途**: 結構編輯器相關樣式
- **主要類名**:
  - `.editStructure`: 編輯結構容器
  - `.editStructureNavbar`: 導航欄樣式
  - `.editStructureMain`: 主要內容區域
  - `.editStructureFilterMenu`: 過濾選單樣式

### 3. structure-tag.module.css
- **用途**: 結構標籤相關樣式
- **主要類名**:
  - `.structureTag`: 結構標籤基礎樣式
  - `.structureTagIcon`: 標籤圖示樣式
  - `.checkCountBall`: 計數球樣式

### 4. filter-select.module.css
- **用途**: 過濾選擇器相關樣式
- **主要類名**:
  - `.filterSelect`: 過濾選擇器樣式
  - `.filterSelectDropdown`: 下拉選單樣式

## 🚀 遷移步驟

### 步驟 1: 更新元件引入
```typescript
// 舊方式 (全局 CSS)
// 無需引入，直接使用類名

// 新方式 (CSS Modules)
import styles from '@/styles/components/form/config-form.module.css'
```

### 步驟 2: 更新類名使用
```typescript
// 舊方式
<div className="edit-structure">
  <nav className="edit-structure-navbar">

// 新方式
<div className={styles.editStructure}>
  <nav className={styles.editStructureNavbar}>
```

### 步驟 3: 處理全局樣式
對於需要影響 Antd 元件的樣式，使用 `:global()` 語法：
```css
.filterSelect :global(.ant-select.ant-tree-select) {
  /* Antd 元件樣式覆蓋 */
}
```

## ⚠️ 注意事項

1. **CSS 變數**: 所有 CSS 變數 (如 `var(--color-gray_3)`) 保持不變
2. **圖片路徑**: 注意調整相對路徑，確保圖片資源正確載入
3. **全局樣式**: 使用 `:global()` 語法處理 Antd 元件樣式
4. **層級管理**: 所有模組都使用 `@layer components` 確保優先級

## 🔍 驗證清單

- [ ] 所有樣式正確分類到對應模組
- [ ] CSS 變數引用正確
- [ ] 圖片路徑調整正確
- [ ] Antd 元件樣式覆蓋正常
- [ ] 響應式樣式保持有效
- [ ] 無重複樣式定義

## 📚 相關文檔

- [CSS Modules 官方文檔](https://github.com/css-modules/css-modules)
- [Vite CSS Modules 支援](https://vitejs.dev/guide/features.html#css-modules)
- [CSS Layers 使用指南](./css-layers-guide.md)
