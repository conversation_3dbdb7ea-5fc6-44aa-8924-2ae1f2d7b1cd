# CSS Layers 解決方案指南

## 🎯 概述

本指南說明如何使用 CSS Layers 來解決 Antd 樣式優先級問題，確保自定義樣式能夠正確覆蓋 Antd 的預設樣式。

## 📋 實施方案

### 1. CSS Layers 層級結構

我們定義了以下 CSS 層級順序（優先級從低到高）：

```css
@layer reset, antd, base, components, utilities, overrides;
```

#### 層級說明：

- **reset**: CSS 重置樣式（最低優先級）
- **antd**: Antd 元件庫樣式
- **base**: 全局基礎樣式（變數、字體等）
- **components**: 自定義元件樣式
- **utilities**: 工具類樣式
- **overrides**: 特殊覆蓋樣式（最高優先級）

### 2. 文件結構

```
src/
├── styles/
│   ├── layers.css          # CSS Layers 全局配置
│   ├── index.css           # 基礎樣式
│   └── glass-style.css     # 玻璃擬態樣式變數
├── components/
│   └── Tabs/
│       ├── ConfigSubTabs.tsx
│       └── ConfigSubTabs.module.css  # 使用 CSS Layers
```

### 3. 使用方式

#### 在 CSS Modules 中使用：

```css
/* ConfigSubTabs.module.css */
@layer antd, custom;

@layer custom {
  .configSubTabs :global(.ant-tabs-nav) {
    margin-bottom: 1.5rem;
    /* 這些樣式會覆蓋 Antd 的預設樣式 */
  }
  
  .configSubTabs :global(.ant-tabs-tab.ant-tabs-tab-active) {
    background: rgba(48, 48, 48, 0.4);
    backdrop-filter: blur(2px) saturate(180%);
  }
}
```

#### 在 React 元件中配置：

```typescript
// ConfigSubTabs.tsx
<ConfigProvider 
  theme={{
    components: {
      Tabs: {
        // Antd 主題配置
      },
    },
    cssVar: {
      prefix: 'ant',
    },
    hashed: false,
  }}
>
  <Tabs className={styles.configSubTabs} />
</ConfigProvider>
```

## 🔧 技術原理

### CSS Layers 優先級規則

1. **層級順序決定優先級**：後定義的層級優先級更高
2. **層級內部遵循傳統 CSS 規則**：選擇器權重、源碼順序等
3. **跨層級比較**：高層級的樣式總是覆蓋低層級的樣式

### 與傳統方法的比較

| 方法 | 優點 | 缺點 |
|------|------|------|
| `!important` | 立即生效 | 難以維護，破壞 CSS 層疊 |
| 高權重選擇器 | 相對穩定 | 選擇器複雜，維護困難 |
| CSS Layers | 清晰的優先級管理 | 需要現代瀏覽器支援 |

## 📊 瀏覽器支援

CSS Layers 支援情況：
- ✅ Chrome 99+
- ✅ Firefox 97+
- ✅ Safari 15.4+
- ✅ Edge 99+

## 🚀 最佳實踐

### 1. 層級命名規範

```css
/* 推薦：語義化命名 */
@layer reset, vendor, base, components, utilities, overrides;

/* 避免：無意義命名 */
@layer layer1, layer2, layer3;
```

### 2. 樣式組織

```css
/* 在每個 CSS 文件開頭聲明使用的層級 */
@layer components, utilities;

/* 將相關樣式放在同一層級 */
@layer components {
  .button { /* 基礎按鈕樣式 */ }
  .button--primary { /* 主要按鈕樣式 */ }
}

@layer utilities {
  .text-center { text-align: center; }
  .hidden { display: none; }
}
```

### 3. 與 CSS Modules 結合

```css
/* 在 CSS Modules 中使用 */
@layer custom;

@layer custom {
  .component :global(.ant-component) {
    /* 自定義樣式 */
  }
}
```

## 🔍 調試技巧

### 1. 檢查層級順序

在瀏覽器開發者工具中：
1. 打開 Elements 面板
2. 查看 Computed 樣式
3. 確認樣式來源和層級

### 2. 驗證樣式覆蓋

```css
/* 添加明顯的測試樣式 */
@layer custom {
  .test :global(.ant-tabs-nav) {
    border: 3px solid red !important;
    background: yellow !important;
  }
}
```

## ⚠️ 注意事項

1. **瀏覽器兼容性**：確保目標瀏覽器支援 CSS Layers
2. **層級順序**：一旦定義，層級順序應保持穩定
3. **性能考量**：過多的層級可能影響 CSS 解析性能
4. **團隊協作**：建立明確的層級使用規範

## 📚 參考資源

- [Antd CSS Layers 文檔](https://ant-design.antgroup.com/docs/react/compatible-style-cn#layer-%E6%A0%B7%E5%BC%8F%E4%BC%98%E5%85%88%E7%BA%A7%E9%99%8D%E6%9D%83)
- [MDN CSS Layers](https://developer.mozilla.org/en-US/docs/Web/CSS/@layer)
- [CSS Layers 完整指南](https://css-tricks.com/css-cascade-layers/)
