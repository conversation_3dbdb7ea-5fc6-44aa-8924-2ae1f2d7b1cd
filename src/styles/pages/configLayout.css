.edit-form .ant-form-item.edit-icon .ant-form-item-control-input {
  position: relative;
  &:hover:before {
    content: url("../../assets/icons/edit.svg");
    position: absolute;
    right: 0;
    top: 1px;
    color: #fff;
  }
}

.ant-form-item .ant-form-item-row:has(.ant-form-item-label label) .ant-form-item-control-input-content{
    display: flex;
    gap: 8px;
    align-items: center;
    color: var(--color-gray_0);
}

#config-sub-tabs.ant-tabs.ant-tabs-top.ant-tabs-card > .ant-tabs-nav {
  margin-bottom: 1.5rem;
  /* background: url("https://images.unsplash.com/photo-1588943211346-0908a1fb0b01?crop=entropy&cs=srgb&fm=jpg&ixid=M3wzMjM4NDZ8MHwxfHJhbmRvbXx8fHx8fHx8fDE3NDk1MzU4MDV8&ixlib=rb-4.1.0&q=85") center/cover; */
  &::before {
    border: none;
  }
  .ant-tabs-nav-list {
    position: relative;
    padding: 4px;
    z-index: 1;
    border-radius: 100px;
    box-shadow: 0 6px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1);

    &::after, &::before {
      content: '';
      position: absolute;
      border-radius: 100px;
      width: 100%;
      height: 100%;
      inset: 0;
      left: 0;
      top: 0;
    }

    &::before {
      z-index: 5;
      background: var(--lg-bg-color);
      backdrop-filter: blur(4px);
      box-shadow: inset 1px 1px 0 var(--lg-highlight),
        inset 0 0 8px var(--lg-highlight);
    }

    &::after {
      z-index: 1;
      filter: url(#lensFilter) saturate(120%) brightness(1.15);
    }
  }

  .ant-tabs-tab {
    position: relative;
    z-index: 5;
    padding: 0 0.5rem;
    border-radius: 100px;
    overflow: hidden;
    transition: transform 200ms ;

    :is(.config-tab-label) {
        transform: scale(.95);
        transition: transform 200ms ;
      }
    }
  
  .ant-tabs-tab.ant-tabs-tab-active {
    background: rgba(48, 48, 48, 0.4);
    backdrop-filter: blur(2px) saturate(180%);
    border: transparent;
    .ant-tabs-tab-btn > span {
      color: var(--color-gray_0);
    }
  }
}

.edit-structure {
  background: var(--color-gray_3);
  border-radius: 5px;
  height: calc(100% - 60px);
  container-type: size;
  display: flex;
  flex-direction: column;
}

.edit-structure-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  margin: 0;
  border-bottom: 2px solid var(--color-gray_4);
  background: var(--color-gray_3);
}

.no-checkbox-border.ant-checkbox-wrapper {
  background: var(--color-gray_5);
  box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4);
  border-radius: 4px;
  transition: background 0.3s;

  .ant-checkbox.ant-wave-target {
    display: none;
  }
  &:has(.ant-checkbox-checked) {
    background: var(--color-gray_2);
  }

  & span:last-child {
    height: 100%;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding-inline-start: 0.25rem;
    padding-inline-end: 0.75rem;
    line-height: 1.5;
  }
}

.edit-structure-filter-menu {
  & li:has(.ant-input) {
    position: relative;

    &::before {
      position: absolute;
      top: 3px;
      left: 10px;
      z-index: 5;
      width: 1rem;
      height: 1rem;
    }

    .ant-input {
      padding: 0 0.75rem 0 2rem;
      height: 100%;
      vertical-align: middle;
    }
  }
}

.edit-structure-main {
  padding: 0.5rem 1.25rem;
  background: var(--color-gray_3);
  overflow-y: auto;
  flex: 1;
}

.structure-tag-icon {
  width: 16px;
  height: 16px;
  transition: all 0.2s;
}

.structure-tag-icon.tag-checkable {
  background-image: url("../../assets/icons/ok-circle-2.svg");
}

.structure-tag:hover .structure-tag-icon {
  background-image: url("../../assets/icons/ok-circle-2.svg");
}

.structure-tag:hover .structure-tag-icon.tag-checkable {
  background-image: url("../../assets/icons/x-circle.svg");
}

.structure-tag.tag-checkable:hover .structure-tag-icon.tag-checkable {
  background-image: url("../../assets/icons/ok-circle-2.svg");
}

.ant-select.ant-tree-select.filter-select {
  min-width: 108px;
  width: auto;

  .ant-select-selector {
    padding: 0 1.75rem 0 0.25rem;
    box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4);

    &::before {
      content: url("../../assets/icons/filter.svg");
      width: 28px;
      height: 28px;
      margin-right: 0.25rem;
    }

    .ant-select-selection-overflow {
      flex-wrap: nowrap;
      gap: 0.25rem;
    }
  }

  &.ant-select-focused .ant-select-selector {
    border: solid 1px var(--color-primary);
    box-shadow: none;
  }

  .ant-select-selection-item {
    height: 20px;
    margin: 0rem 0;
    border-radius: 1.25rem;
    background-color: rgba(255, 255, 255, 0.25);
    padding: 0 0.25rem 0 0.5rem;
    .ant-select-selection-item-content {
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.ant-tree-select-dropdown.filter-select-dropdown {
  padding: 0.25rem 0;
}

.ant-tree-select-dropdown.filter-select-dropdown .ant-select-tree {
  .ant-select-tree-treenode {
    padding: 0.25rem 0.75rem;
    &:first-child {
      padding-bottom: 8px;
      border-bottom: 2px solid var(--color-gray_3);
    }
  }
  .ant-select-tree-indent,
  .ant-select-tree-switcher.ant-select-tree-switcher-noop,
  .ant-select-tree-switcher.ant-select-tree-switcher_open {
    display: none;
  }
}

.filter-select-dropdown.ant-select-dropdown.ant-cascader-dropdown .ant-cascader-menu {
  height: auto;
}

.check-count-ball {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  font-size: 12px;
  border-radius: 50%;
  background: var(--color-gray_0_alpha);
  color: var(--color-gray_0);
}
