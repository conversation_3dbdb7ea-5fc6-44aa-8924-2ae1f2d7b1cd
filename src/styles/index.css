@layer antd, custom, glass;

/************************** basic **************************/

* {
  scrollbar-color: var(--scroll-bar-color);
  text-align: left;
}

*::-webkit-scrollbar-track,
*::-webkit-scrollbar-corner {
  border-radius: 10px;
  background-color: transparent;
}

*::-webkit-scrollbar {
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--color-gray_3);
}

*::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: var(--scroll-bar-color);
}

p {
  margin: 0;
}

#manual-wrapper-layout {
  white-space: pre-wrap;
}
#manual-wrapper-layout :is(h1, h2, h3, h4, h5, h6) {
  /* scroll-margin-top: 16px; */
}

.ant-layout {
  background: var(--color-gray_4);
  min-height: 900px;
  height: 100vh;
}

.ant-layout-content {
  display: flex;
  flex-direction: column;
  padding: 2rem 2rem 2.5rem;

  @media (max-width: 992px) {
    padding: 2rem 1rem 2.5rem;
  }
}

@media print {
  .print-none,
  #manual-wrapper-layout {
    display: none !important;
  }
}

/* empty */
.ant-empty .ant-empty-image {
  height: auto;
}

/* form */
.custom-checkbox:hover .ant-checkbox-inner {
  border-color: var(--color-primary);
}

.ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless):is(.ant-input:focus, .ant-input-focused) {
  border: 1px solid #dc4446 !important;
}

.ant-form-item :has(.ant-form-item-control-input .ant-input[disabled]) {
  .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional) {
    color: rgba(192, 192, 192, 0.5);
    cursor: not-allowed;

    &::before {
      color: rgba(192, 192, 192, 0.5);
      cursor: not-allowed;
    }
  }
}

/* switch */
.ant-switch {
  width: 32px;
  border: 1px solid white;
}

.ant-switch.ant-switch-disabled {
  opacity: 1;
  --color-primary: var(--color-primary_variants);
  background: var(--color-gray_1_variants);
  border-color: var(--color-gray_1);
}

.ant-switch.ant-switch-disabled .ant-switch-handle::before {
  background: var(--color-gray_1);
}

/************************ utilites ************************/
/* text */
.text-black {
  color: #000 !important;
}

.text-white {
  color: var(--color-gray_0) !important;
}

.hover\:text-white:hover {
  color: var(--color-gray_0) !important;
}

.text-gray-3 {
  color: var(--color-gray_3) !important;
}

.text-gray-4 {
  color: var(--color-gray_4) !important;
}

.text-gray-5 {
  color: var(--color-gray_5) !important;
}

.text-center {
  text-align: center;
}

.text-disabled {
  color: rgba(255, 255, 255, 0.3) !important;
}

/* input */
.input-style-default {
  background: transparent;
  outline: none;
  border-color: transparent;
}

.input-none-style {
  background: transparent;
  outline: transparent;
  border-color: transparent;
  padding: 0.75rem 0.25rem;
  transition: all 0.3s;
  border-radius: 4px;

  &.focus-default:focus {
    border: none;

    &:focus {
      outline: solid 1px var(--color-gray_1);
    }
  }
}

.p-0 {
  padding: 0;
}

.user-select-none {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
}

/*********************************************** 
                    layout 
***********************************************/
/* pages header */
.head {
  background: var(--color-gray_4);
  overflow-x: auto;
  height: auto;
}

.head nav {
  padding: 0 2rem;
  display: flex;
  justify-content: space-around;
  min-width: 480px;
}

.head nav h1 {
  margin: 0 !important;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16px;
}

.head nav button {
  background: transparent !important;
}

.head-navbar {
  margin: 0;
  display: flex;
  justify-content: end;
  align-items: center;
  list-style-type: none;
  padding: 0;
  gap: 0.75rem;
}

.head-navbar li,
.head-navbar button {
  position: relative;
  margin: 0;
  height: 100%;
  padding: 0 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.head-navbar button {
  background: transparent !important;
  border: none;
  outline: none;
}

.head-navbar-list {
  width: 100%;
  padding: 0.75rem;
  margin: 0;
  position: absolute;
  top: 100%;
  line-height: 150%;
  background: var(--color-gray_2);
  /* box-shadow: 0 1px 2px 3px rgba(0, 0, 0, 0.1); */
  box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4), 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  transition: all 0.16s;
}

.head-navbar-list:hover {
  background: var(--color-gray_3);
  box-shadow: 0px 2px 1px 1px rgba(26, 38, 47, 0.4), 0px 4px 4px 1px rgba(0, 0, 0, 0.25);
  color: var(--color-primary);
}

/* dropdown */
.ant-dropdown .ant-dropdown-menu {
  padding: 0;
  background: var(--color-gray_3);
  box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4), 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}

.ant-dropdown .ant-dropdown-menu a {
  display: block;
  padding: 0.25rem 0.75rem;
  text-align: center;
}

.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item {
  transition: all.16s;
}

.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
  background: var(--color-gray_3);
  color: var(--color-primary);
}

.ant-badge .ant-badge-count {
  color: var(--color-gray_0);
}

/* navigation-sidebar */
.navigation-sidebar {
  background: var(--color-gray_5) !important;
}

.navigation-sidebar .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.navigation-sidebar .collapsed-btn.ant-btn.ant-btn-text.ant-btn-icon-only {
  position: absolute;
  z-index: 20;
  right: 0;
  bottom: 140px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 1.5rem;
  border-radius: 5px 0px 0px 5px;
  border-top: 2px solid var(--color-gray_2);
  border-bottom: 2px solid var(--color-gray_2);
  border-left: 2px solid var(--color-gray_2);

  .anticon {
    color: rgba(255, 255, 255, 0.5);
    font-size: 1rem;
  }
}

.navigation-sidebar-footer {
  padding: 18px 1rem 0;
  margin-bottom: 24px;
  border-top: 2px solid var(--color-gray_3);
}

@keyframes textOpacity {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.sider-header {
  margin-bottom: 32px;
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  color: var(--color-primary);
}

.sider-header p {
  position: absolute;
  right: 0;
  margin: 0;
  color: var(--color-primary);
  font-weight: bold;
  transition: opacity 0.2s;
}

.sider-header p.false {
  opacity: 0;
}

.sider-header p.true {
  opacity: 1;
}

.logo-box {
  height: 44px;
  background: url("/images/logo.svg") no-repeat;
  background-size: 115px 44px;
}

.big-logo {
  width: 44px;
}

.default-logo {
  width: 115px;
}

/* table container */
.table-container {
  position: relative;
  width: 100%;
  min-width: 882px;
  color: #fff;
  /* margin:  2.5rem 0 0; */

  & thead {
    position: sticky;
    top: 0;
    color: rgba(192, 192, 192, 0.5);
    background-color: var(--color-gray_4);
    font-size: 14px;
    line-height: 2;
    white-space: nowrap;
    z-index: 5;

    &::after,
    &::before {
      position: absolute;
      content: "";
      width: 100%;
    }

    &::after {
      border-top: var(--color-gray_2) solid 1px;
      border-bottom: var(--color-gray_2) solid 1px;
    }

    &::before {
      border-top: var(--color-gray_2) solid 1px;
      border-bottom: var(--color-gray_2) solid 1px;
    }

    & th {
      font-weight: 400 !important;
      text-align: left;
      padding: 0 0.5rem;
    }
  }

  & tbody tr {
    border-bottom: var(--color-gray_2) solid 1px;
    transition: all 0.12s;

    &:hover {
      background: var(--color-gray_2);
    }
  }

  & td {
    padding: 0.75rem 0.5rem;
  }
}

.ant-table-wrapper .ant-table-thead > tr > :is(th, td),
.ant-table-wrapper .ant-table-thead > tr > td {
  white-space: nowrap;
  /* border-top: 1px solid rgba(255, 255, 255, 0.5);
  border-bottom: 1px solid rgba(255, 255, 255, 0.5); */
  line-height: 2.5;
  padding-top: 0;
  padding-bottom: 0;

  .ant-table-column-title {
    white-space: nowrap;
  }
}

.ant-table-wrapper thead.ant-table-thead > tr > th.ant-table-cell {
  color: rgba(192, 192, 192, 0.5);
  font-size: 14px;
  font-weight: normal;
}

.ant-table-wrapper .ant-table-column-sorters .ant-table-column-sorter {
  color: rgba(255, 255, 255, 0.3);
}

.ant-table-wrapper .ant-table-column-sorters:hover .ant-table-column-sorter {
  color: rgba(255, 255, 255, 0.3);
}

/* ColorPicker */
.ant-color-picker-trigger .ant-color-picker-trigger-text {
  display: flex;
  align-items: center;
}

/* page footer */
.layout-footer {
  display: flex;
  justify-content: end;
  /* margin-top: 3rem; */
  padding: 2rem 0 0;
}

/* error msg */
.ant-form-item .ant-form-item-explain-error {
  color: var(--color-error);
}

.ant-form-item-control .ant-form-item-explain.ant-form-item-explain-connected:not(#login_account_help) {
  position: absolute;
}

/* remove css default */
.remove-spinners {
  /* Chrome, Safari, Edge, Opera */
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  :is(input[type="number"]) {
    appearance: textfield;
    -moz-appearance: textfield;
  }
}

/* scroll bar */
.scrollbar-noStyle {
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    background-color: transparent;
  }
}

section:has(.table-container) {
  &::-webkit-scrollbar {
    height: 8px;
    background-color: transparent;
  }
}

/*  date  */
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-in-range::before,
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before,
.ant-picker-dropdown .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before,
.ant-picker-dropdown .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
  background: var(--color-gray_4);
}

.ant-picker-ok .ant-btn.ant-btn-sm.ant-btn-primary {
  box-shadow: none;
  border-radius: 0.25rem;
  padding: 0 0.75rem;
  height: auto;
  transition: all 0.16s;

  &:hover {
    background: var(--color-primary_variants);
  }
}

.custom-date-picker.ant-picker-dropdown :is(.ant-picker-cell .ant-picker-cell-inner, .ant-picker-date-panel .ant-picker-content th) {
  text-align: center;
}

.custom-date-picker.ant-picker-dropdown
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before {
  background: var(--color-primary_variants);
}

.ant-picker .ant-picker-input > input {
  font-size: 14px;

  &:placeholder-shown {
    text-overflow: clip;
  }
}

/* workList history */
.series-navbar {
  display: flex;
  gap: 2.5rem;
  margin-left: 0.25rem;
  overflow-x: auto;
}

.series-navbar-title {
  font-size: 14px;
  color: rgba(192, 192, 192, 0.5);
  margin-bottom: 0.5rem;
  white-space: nowrap;
}

.series-navbar-content {
  font-size: 1rem;
  color: var(--color-gray_0);
  line-height: 2;
  white-space: nowrap;
}

.selected-row {
  background: var(--color-gray_3) !important;
}

.ant-table-column-title {
  user-select: none !important;
}


.study-table-container.ant-table-wrapper .ant-table-container {
  overflow: auto;
}

.study-table-container.ant-table-wrapper .ant-table-container table {
  border-top: 1px solid var(--color-gray_3);
  border-radius: 0;

  .ant-table-thead {
    & tr:first-child > :is(*:first-child, *:last-child) {
      border-start-start-radius: 0;
      border-start-end-radius: 0;
    }

    :is(th, td) {
      padding: 0.25rem 1rem;
    }

    .ant-table-column-title {
      white-space: nowrap;
    }
  }

  .ant-table-tbody {
    .ant-table-row .ant-table-cell {
      padding: 0.75rem 1rem;
    }

    .ant-table-cell:first-child {
      width: 32px;
      padding: 0;
    }

    .ant-table-cell .icon-box {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

.series-table-container.ant-table-wrapper {
  height: calc(100% - 96px);
}

.series-table-container.ant-table-wrapper :is(.ant-table-container, .ant-spin-container, .ant-table) {
  height: 100%;
  container-type: size;
}

/* Notification */

.ant-notification-notice-with-icon {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.ant-notification .ant-notification-notice .ant-notification-notice-with-icon .ant-notification-notice-description,
.ant-notification .ant-notification-notice .ant-notification-notice-with-icon .ant-notification-notice-message {
  margin-inline-start: 30px;
}

.ant-notification .ant-notification-notice .ant-notification-notice-icon {
  top: 25px;
  left: 25px;
}

/* Tabs */
.setting-change-alert {
  position: absolute;
  z-index: 10;
  right: 0;
  top: 8px;
  flex-direction: row-reverse;
  gap: 8px;
  align-items: center;
  box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4);
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  90% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.fade-enter {
  opacity: 0;
  transform: translateX(20px);
}
.fade-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.5s, transform 0.5s;
}
.fade-exit {
  opacity: 1;
}
.fade-exit-active {
  opacity: 0;
  transform: translateX(20px);
  transition: opacity 0.5s;
}

@keyframes loading {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.loading-wrapper {
  position: absolute;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  background-color: var(--color-gray_5);
  box-shadow: 0 2px 4px 1px rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 0.5rem;
  padding: 2.5rem;
}

.loading-conten p {
  width: 300px;
  text-align: center;
  color: var(--color-gray_0);
}
.loading-animation svg {
  animation: loading 2s infinite linear;
}

.ant-spin .ant-spin-dot {
  font-size: 48px;
}
.spin-animation {
  width: 48px;
  height: 48px;
  font-size: 48px;
  fill: var(--color-primary);
  animation: loading 2s infinite linear;
}

/* circle box */
.circle-group {
  position: relative;
  height: 250px;
  width: 250px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: 5px solid var(--color-primary);
  text-align: center;
  color: var(--color-gray_0) !important;
}

.circle-group h2 {
  font-size: 5rem;
  margin: 1rem 0;
}

.circle-group p {
  opacity: 0.5;
}

.circle-group::after {
  content: "";
  position: absolute;
  width: 98%;
  top: 62%;
  left: 2px;
  border: 2px solid var(--color-primary);
}

.modal-footer-btn-cursor .ant-modal-footer .ant-btn {
  cursor: pointer;
}

.red-ball {
  position: relative;
}

.red-ball::before {
  content: "";
  position: absolute;
  background-color: var(--color-error);
  width: 6px;
  height: 6px;
  right: -8px;
  border-radius: 50%;
}

.red-ball.right-move::before {
  right: -6px;
}

.study-info-radio {
  display: flex;
  gap: 0.5rem;
}

.study-info-radio .ant-radio-button-wrapper {
  padding: 0;
  padding-inline: 0;
  width: 5rem;
  text-align: center;
  border: 1px solid rgba(192, 192, 192, 0.5);
  border-radius: 0.25rem;
  color: rgba(192, 192, 192, 0.5);
  background: transparent;
  transition: 0.3s all;
}

.study-info-radio .ant-radio-button-wrapper.ant-radio-button-wrapper-checked {
  border-radius: 0.25rem;
  border: 1px solid var(--color-gray_1);
  background: transparent;
  color: #fff;
}
.study-info-radio .ant-radio-button-wrapper::before {
  width: 0;
}

.study-info-radio .ant-radio-button-wrapper.ant-radio-button-wrapper-checked span:not(.ant-radio-button, .ant-radio-button-inner) {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &::before {
    content: "";
    display: block;
    background-color: #fff;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
}

#add-list-modal-radio .ant-radio.ant-radio-checked > .ant-radio-inner {
  background-color: transparent;
  color: #ffffff;
  border-color: transparent;
  background-image: url("/src/assets/icons/ok-circle.svg");
  background-size: 20px;
  background-position: center; /* 置中背景圖片 */
  background-repeat: no-repeat; /* 禁止重複 */
  filter: brightness(0) invert(1);
}

#add-list-modal-radio .ant-radio.ant-radio-checked > .ant-radio-inner::after {
  background-color: transparent;
  opacity: 0;
}

.ant-list {
  flex: 1;
}

.scroll-area-tabs.ant-tabs {
  height: 100%;

  .ant-tabs-content-holder {
    overflow: auto;
  }
}

.radio-not-center-driver .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
  background-color: transparent;
}

.select-multiple-tags.ant-select-dropdown
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled)
  .ant-select-item-option-state {
  display: none;
}

/* util */
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.flex-col-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.prefix-center {
  .ant-select-prefix,
  .ant-input-prefix {
    display: flex;
  }
}

.transition-enter {
  transition: all 0.3s;
}

.basic-shadow {
  box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4);
}

.placeholder-gray-1 .ant-input::placeholder {
  color: var(--color-gray_1);
}
