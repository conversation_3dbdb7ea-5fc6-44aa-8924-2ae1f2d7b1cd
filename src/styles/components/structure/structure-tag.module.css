/*
 * Structure Tag 相關樣式
 * 用於結構標籤元件
 */

/* 定義 CSS 層級 */
@layer components;

@layer components {
  /* 結構標籤圖示 */
  .structureTagIcon {
    width: 16px;
    height: 16px;
    transition: all 0.2s;
  }

  .structureTagIcon.tagCheckable {
    background-image: url("../../../assets/icons/ok-circle-2.svg");
  }

  /* 結構標籤懸停效果 */
  .structureTag:hover .structureTagIcon {
    background-image: url("../../../assets/icons/ok-circle-2.svg");
  }

  .structureTag:hover .structureTagIcon.tagCheckable {
    background-image: url("../../../assets/icons/x-circle.svg");
  }

  .structureTag.tagCheckable:hover .structureTagIcon.tagCheckable {
    background-image: url("../../../assets/icons/ok-circle-2.svg");
  }

  /* 檢查計數球 */
  .checkCountBall {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    font-size: 12px;
    border-radius: 50%;
    background: var(--color-gray_0_alpha);
    color: var(--color-gray_0);
  }
}
