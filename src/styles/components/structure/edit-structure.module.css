/*
 * Edit Structure 相關樣式
 * 用於結構編輯元件
 */

/* 定義 CSS 層級 */
@layer components;

@layer components {
  /* 編輯結構容器 */
  .editStructure {
    background: var(--color-gray_3);
    border-radius: 5px;
    height: calc(100% - 60px);
    container-type: size;
    display: flex;
    flex-direction: column;
  }

  /* 編輯結構導航欄 */
  .editStructureNavbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    margin: 0;
    border-bottom: 2px solid var(--color-gray_4);
    background: var(--color-gray_3);
  }

  /* 編輯結構過濾選單 */
  .editStructureFilterMenu {
    & li:has(.ant-input) {
      position: relative;

      &::before {
        position: absolute;
        top: 3px;
        left: 10px;
        z-index: 5;
        width: 1rem;
        height: 1rem;
      }

      .ant-input {
        padding: 0 0.75rem 0 2rem;
        height: 100%;
        vertical-align: middle;
      }
    }
  }

  /* 編輯結構主要內容區域 */
  .editStructureMain {
    padding: 0.5rem 1.25rem;
    background: var(--color-gray_3);
    overflow-y: auto;
    flex: 1;
  }
}
