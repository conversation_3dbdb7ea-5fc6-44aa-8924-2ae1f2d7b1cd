/*
 * Filter Select 相關樣式
 * 用於過濾選擇器元件
 */

/* 定義 CSS 層級 */
@layer components;

@layer components {
  /* 過濾選擇器主體 */
  .filterSelect :global(.ant-select.ant-tree-select) {
    min-width: 108px;
    width: auto;

    .ant-select-selector {
      padding: 0 1.75rem 0 0.25rem;
      box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4);

      &::before {
        content: url("../../../assets/icons/filter.svg");
        width: 28px;
        height: 28px;
        margin-right: 0.25rem;
      }

      .ant-select-selection-overflow {
        flex-wrap: nowrap;
        gap: 0.25rem;
      }
    }

    &.ant-select-focused .ant-select-selector {
      border: solid 1px var(--color-primary);
      box-shadow: none;
    }

    .ant-select-selection-item {
      height: 20px;
      margin: 0rem 0;
      border-radius: 1.25rem;
      background-color: rgba(255, 255, 255, 0.25);
      padding: 0 0.25rem 0 0.5rem;
      
      .ant-select-selection-item-content {
        font-size: 14px;
        line-height: 20px;
      }
    }
  }

  /* 過濾選擇器下拉選單 */
  .filterSelectDropdown :global(.ant-tree-select-dropdown) {
    padding: 0.25rem 0;

    .ant-select-tree {
      .ant-select-tree-treenode {
        padding: 0.25rem 0.75rem;
        
        &:first-child {
          padding-bottom: 8px;
          border-bottom: 2px solid var(--color-gray_3);
        }
      }
      
      .ant-select-tree-indent,
      .ant-select-tree-switcher.ant-select-tree-switcher-noop,
      .ant-select-tree-switcher.ant-select-tree-switcher_open {
        display: none;
      }
    }
  }

  /* 級聯選擇器下拉選單 */
  .filterSelectDropdown :global(.ant-select-dropdown.ant-cascader-dropdown) {
    .ant-cascader-menu {
      height: auto;
    }
  }
}
