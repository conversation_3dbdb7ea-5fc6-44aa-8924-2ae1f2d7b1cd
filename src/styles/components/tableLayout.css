.ant-table {
  flex: 1;
}

.setting-table-container.ant-table-wrapper {
  height: 100%;
}

.setting-table-container .ant-table-cell {
  border-top: 1px solid #5a656d;
}
.dnd-table.setting-table-container .ant-table-thead .ant-table-cell {
  border-color: var(--color-gray_1);
}

.setting-table-container .ant-table-container {
  container-type: size;
  height: 100%;
  min-height: 350px;
  overflow: auto;
}

/* thead form  */
.add-newRow-table.ant-table-wrapper {
  .ant-table-thead > tr > :is(th, td) {
    background-color: transparent;
  }
  .ant-table-cell {
    vertical-align: top;
  }

  :is(tbody.ant-table-tbody, thead.ant-table-thead) :is(.ant-table-cell):last-child {
    text-align: center;
  }
}
