/*
 * Config Form 相關樣式
 * 用於配置頁面的表單元件
 */

/* 定義 CSS 層級 */
@layer components;

@layer components {
  /* 表單項目控制輸入內容樣式 */
  .formItemControl :global(.ant-form-item .ant-form-item-row:has(.ant-form-item-label label)) {
    .ant-form-item-control-input-content {
      display: flex;
      gap: 8px;
      align-items: center;
      color: var(--color-gray_0);
    }
  }

  /* 無邊框複選框樣式 */
  .noCheckboxBorder :global(.ant-checkbox-wrapper) {
    background: var(--color-gray_5);
    box-shadow: 0px 2px 0px 0px rgba(26, 38, 47, 0.4);
    border-radius: 4px;
    transition: background 0.3s;

    .ant-checkbox.ant-wave-target {
      display: none;
    }

    &:has(.ant-checkbox-checked) {
      background: var(--color-gray_2);
    }

    & span:last-child {
      height: 100%;
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding-inline-start: 0.25rem;
      padding-inline-end: 0.75rem;
      line-height: 1.5;
    }
  }
}
