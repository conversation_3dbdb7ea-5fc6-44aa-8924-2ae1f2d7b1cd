import React from 'react'

import { ConfigProvider, theme, App as AntApp } from 'antd'
import ReactDOM from 'react-dom/client'
import { I18nextProvider } from 'react-i18next'
import { Provider } from 'react-redux'
import { persistStore } from 'redux-persist'
import { PersistGate } from 'redux-persist/lib/integration/react'

import App from './App'
import EmptyBox from './components/EmptyBox'
import i18n from './i18n'
import store from './store'
import { color } from './utils/variables'

import 'antd/dist/reset.css'
import './styles/index.css'
import './styles/glass-style.css'
import './styles/components/button.css'
import './styles/pages/configLayout.css'

// Start MSW in development
async function enableMocking() {
  if (process.env.NODE_ENV !== 'development') return

  const { worker } = await import('./mocks/browser')
  await worker.start({
    onUnhandledRequest: 'bypass',
  })
}

enableMocking().then(() => {
  ReactDOM.createRoot(document.getElementById('root')!).render(
    <React.StrictMode>
      <ConfigProvider
        theme={{
          algorithm: theme.darkAlgorithm,
          token: {
            colorPrimary: color.primary.default,
            colorPrimaryHover: color.primary.dark,
            colorText: color.gray[0].default,
            colorWarning: color.error,
            fontSizeSM: 14,
            fontSize: 16,
            fontSizeLG: 18,
            fontSizeXL: 24,
            fontSizeHeading1: 24,
            fontSizeHeading2: 18,
            fontSizeHeading3: 16,
            fontSizeHeading4: 14,
            fontSizeHeading5: 12,
            borderRadius: 5,
            controlHeightSM: 28,
          },
          components: {
            Badge: {
              colorBorderBg: 'transparent',
            },
            Button: {
              colorBgContainer: 'transparent',
              colorBgContainerDisabled: 'transparent',
              defaultBg: color.gray[1].variants,
              defaultBorderColor: 'transparent',
              defaultHoverBg: color.gray[2],
              defaultHoverBorderColor: 'transparent',
              defaultHoverColor: color.gray[0].default,
              defaultActiveBg: color.gray[2],
              defaultActiveBorderColor: 'transparent',
              defaultActiveColor: color.gray[0].default,
              colorPrimary: color.primary.default,
              colorPrimaryBg: color.primary.default,
              colorPrimaryBorder: 'transparent',
              colorPrimaryHover: color.primary.dark,
              colorPrimaryTextHover: color.primary.dark,
              colorPrimaryActive: color.primary.dark,
              colorPrimaryTextActive: color.primary.dark,
              colorTextDisabled: color.primary.default,
              borderColorDisabled: color.primary.default,
              controlHeightSM: 30,
              onlyIconSizeSM: 30,
            },
            Checkbox: {
              colorBgContainer: color.gray[2],
              colorBorder: color.gray[1].default,
              colorPrimary: color.primary.default,
              colorPrimaryHover: color.primary.default,
            },
            Collapse: {
              headerBg: color.gray[2],
              contentBg: 'transparent',
            },
            ColorPicker: {
              colorBgElevated: 'transparent',
              colorPrimary: color.primary.default,
              colorBorder: color.gray[1].default,
              controlHeightSM: 24,
              controlHeight: 28,
              controlHeightLG: 32,
            },
            DatePicker: {
              colorBgContainer: color.gray[3],
              colorBgElevated: color.gray[2],
              colorBorder: color.gray[1].default,
              colorPrimary: color.primary.default,
              cellRangeBorderColor: color.primary.default,
              cellActiveWithRangeBg: color.primary.dark,
              hoverBg: color.gray[3],
              hoverBorderColor: color.primary.light,
              activeBorderColor: color.primary.default,
              addonBg: color.primary.dark,
              multipleItemBorderColor: color.primary.dark,
              multipleItemBg: color.primary.dark,
            },
            Descriptions: {
              itemPaddingBottom: 24,
              colorTextSecondary: 'rgba(192, 192, 192, 0.5)',
            },
            Drawer: {
              colorBgElevated: color.gray[2],
            },
            Layout: {
              footerBg: 'transparent',
              footerPadding: '1rem 2rem 2.5rem',
            },
            Menu: {
              colorSplit: 'transparent',
              colorBgLayout: color.gray[5],
              colorBgContainer: color.gray[5],
              colorFillAlter: color.gray[5],
              groupTitleColor: color.gray[0].default,
              subMenuItemSelectedColor: color.gray[0].default,
              itemColor: color.gray[0].default,
              itemHoverBg: color.gray[2],
              itemHoverColor: color.gray[0].variants,
              itemActiveBg: color.gray[2],
              itemSelectedBg: color.gray[2],
              itemSelectedColor: color.gray[0].default,
              darkItemSelectedBg: color.gray[0].default,
              popupBg: color.gray[5],
              lineHeight: 40,
            },
            Modal: {
              contentBg: color.gray[3],
              headerBg: color.gray[3],
              footerBg: color.gray[3],
              borderRadius: 10,
              titleFontSize: 18,
              titleLineHeight: 2.75,
              fontWeightStrong: 700,
            },
            Input: {
              colorText: color.gray[0].default,
              colorTextPlaceholder: 'rgba(255, 255, 255, 0.3)',
              colorBgContainer: color.gray[3],
              colorBorder: color.gray[1].default,
              activeBg: color.gray[3],
              hoverBg: color.gray[3],
              hoverBorderColor: color.primary.light,
              activeBorderColor: color.primary.light,
              colorTextDisabled: 'rgba(192, 192, 192, 0.5)',
            },
            InputNumber: {
              colorText: color.gray[0].default,
              colorTextPlaceholder: 'rgba(255, 255, 255, 0.3)',
              colorBgContainer: color.gray[3],
              colorBorder: color.gray[1].default,
              activeBg: color.gray[3],
              hoverBg: color.gray[3],
              hoverBorderColor: color.primary.light,
              activeBorderColor: color.primary.light,
              colorTextDisabled: 'rgba(192, 192, 192, 0.5)',
            },
            Pagination: {
              colorBgContainer: 'transparent',
              colorBorder: 'transparent',
              colorBgTextActive: 'transparent',
              colorPrimary: color.primary.dark,
              colorPrimaryHover: color.primary.dark,
              colorBgTextHover: 'transparent',
              itemSize: 30,
            },
            Popover: {
              colorBgElevated: color.gray[2],
            },
            Radio: {
              buttonBg: 'transparent',
              buttonSolidCheckedBg: color.gray[2],
              buttonSolidCheckedHoverBg: color.gray[2],
              buttonSolidCheckedActiveBg: color.gray[2],
              buttonSolidCheckedColor: color.gray[0].default,
              colorPrimary: color.gray[0].default,
              colorText: color.gray[1].default,
              colorBorder: color.gray[1].variants,
              colorPrimaryHover: color.gray[1].variants,
              colorBorderBg: 'transparent',
              borderRadiusSM: 50,
              fontSize: 14,
            },
            Select: {
              selectorBg: color.gray[3],
              colorBgElevated: color.gray[2],
              colorBorder: color.gray[1].default,
              hoverBorderColor: color.primary.default,
              activeBorderColor: color.primary.default,
              optionSelectedBg: color.primary.default,
            },
            Slider: {
              colorPrimary: color.primary.dark,
              colorPrimaryBorderHover: color.primary.dark,
              railBg: color.gray[2],
              railHoverBg: color.gray[2],
              handleColor: color.primary.dark,
              colorBgElevated: color.primary.dark,
              trackBg: color.gray[2],
              trackHoverBg: color.gray[2],
              handleSize: 16,
              handleSizeHover: 16,
              railSize: 8,
              borderRadiusXS: 16,
            },
            Switch: {
              colorPrimary: color.primary.default,
              colorTextQuaternary: color.gray[1].default,
              handleSize: 12,
              trackHeight: 20,
              trackHeightSM: 20,
              trackMinWidth: 24,
              trackMinWidthSM: 24,
              trackPadding: 3,
            },
            Table: {
              colorBgContainer: color.gray[4],
              stickyScrollBarBg: 'var(--scroll-bar-color) transparent',
              rowHoverBg: color.gray[3],
              bodySortBg: 'transparent',
              colorPrimary: color.primary.default,
              headerBg: color.gray[4],
              headerSortHoverBg: color.gray[3],
              headerSortActiveBg: 'transparent',
              borderColor: 'rgba(255, 255, 255, 0.3)',
              headerBorderRadius: 0,
            },
            Tabs: {
              itemColor: color.gray[0].default,
              itemActiveColor: color.primary.default,
              itemSelectedColor: color.primary.default,
              cardBg: 'transparent',
              colorPrimaryBorder: 'transparent',
              colorBorderSecondary: 'rgba(255, 255, 255, 0.15)',
              inkBarColor: color.primary.default,
            },
            Tag: {
              defaultBg: color.gray[1].variants,
              defaultColor: color.gray[1].default,
            },
            Tooltip: {
              colorBgSpotlight: color.gray[2],
              fontSize: 12,
            },
          },
        }}
        renderEmpty={() => <EmptyBox />}
      >
        <I18nextProvider i18n={i18n}>
          <Provider store={store}>
            <PersistGate loading={null} persistor={persistStore(store)}>
              <AntApp>
                <App />
              </AntApp>
            </PersistGate>
          </Provider>
        </I18nextProvider>
      </ConfigProvider>
    </React.StrictMode>,
  )
})
