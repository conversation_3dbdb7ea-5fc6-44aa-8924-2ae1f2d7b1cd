import { ModalHooksProps } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'

export const modalFooterButtons = {
  okButtonProps: {
    variant: 'outlined' as const,
    color: 'primary' as const,
    style: {
      width: '6.25rem',
      height: '1.75rem',
    },
  },
  cancelButtonProps: {
    variant: 'outlined' as const,
    color: 'default' as const,
    style: {
      width: '6.25rem',
      height: '1.75rem',
    },
    className: 'btn gray outline',
  },
}

export const resetButton: ModalBtnType = {
  btnName: i18n.t('buttons.reset'),
  modalTitle: i18n.t('modal_titles.reset_confirmation'),
  modalContent: i18n.t('modal_contents.reset_confirmation'),
  modalClassName: 'confirm-modal',
  okText: i18n.t('buttons.reset'),
  modalBodyStyle: {
    display: 'flex',
    flexDirection: 'column',
    gap: '1rem',
  },
}

export const saveButton: ModalBtnType = {
  btnName: i18n.t('buttons.save'),
  modalTitle: i18n.t('modal_titles.save_settings'),
  modalClassName: 'confirm-modal',
  okText: i18n.t('buttons.save'),
  modalContent: i18n.t('modal_contents.save_settings'),
}

export const resetAntModalProps: ModalHooksProps<any> = {
  triggerProps: {
    children: i18n.t('buttons.reset'),
    style: {
      width: '6.25rem',
      lineHeight: 1.15,
      height: '2rem',
    },
    color: 'primary',
    variant: 'outlined',
  },
  modalProps: {
    title: i18n.t('modal_titles.reset_confirmation'),
    children: i18n.t('modal_contents.reset_confirmation'),
    className: 'confirm-modal',
    okText: i18n.t('buttons.reset'),
    style: { maxWidth: '21.75rem' },
    centered: true,
    destroyOnClose: true,
  },
}

export const saveAntModalProps: ModalHooksProps<any> = {
  triggerProps: {
    children: i18n.t('buttons.save'),
    style: {
      width: '6.25rem',
      lineHeight: 1.15,
      height: '2rem',
    },
    color: 'primary',
    variant: 'outlined',
  },
  modalProps: {
    title: i18n.t('modal_titles.save_settings'),
    children: i18n.t('modal_contents.save_settings'),
    okText: i18n.t('buttons.save'),
    width: 350,
    style: { maxWidth: '21.75rem' },
    styles: {
      body: { fontSize: '18px', padding: '5rem 2rem' },
    },
    centered: true,
    destroyOnClose: true,
  },
}

export const baseTriggerProps = {
  className: 'basic-shadow icon-only ',
  size: 'small' as const,
  style: { width: 32, height: 32 },
}
