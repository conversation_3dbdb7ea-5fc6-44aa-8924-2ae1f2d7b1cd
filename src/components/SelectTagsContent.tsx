import styles from '@/styles/layout/select-tags-content.module.css'
import { StructuresTagList, StructuresTagProps } from 'src/components/Tag/StructuresTag'

type CheckedTagsType = StructureConfigType & {
  checked: boolean
}

interface Props {
  dataSource: CheckedTagsType[]
  children?: React.ReactNode
  structuresTagProps?: Partial<Omit<StructuresTagProps, 'tagData'>>
}

function SelectTagsContent({
  dataSource, children, structuresTagProps,
}: Props) {
  return (
    <section className={styles.SelectTagsContent}>
      {children}

      {/* select main */}
      <main>
        <StructuresTagList dataSource={dataSource} tagProps={structuresTagProps} />
      </main>
    </section>
  )
}

export default SelectTagsContent
