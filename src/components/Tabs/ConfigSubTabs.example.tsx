import React from 'react'
import { Button, Form, Input, Space } from 'antd'

import ConfigSubTabs from './ConfigSubTabs'

// 使用示例：展示遷移後的 ConfigSubTabs 元件
function ConfigSubTabsExample() {
  const tabItems = [
    {
      key: '1',
      label: (
        <span className="config-tab-label">
          基本設定
        </span>
      ),
      children: (
        <div style={{ padding: '1rem' }}>
          <Form layout="vertical">
            <Form.Item label="名稱" name="name">
              <Input placeholder="請輸入名稱" />
            </Form.Item>
            <Form.Item label="描述" name="description">
              <Input.TextArea placeholder="請輸入描述" rows={4} />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary">保存</Button>
                <Button>取消</Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <span className="config-tab-label">
          進階設定
        </span>
      ),
      children: (
        <div style={{ padding: '1rem' }}>
          <Form layout="vertical">
            <Form.Item label="API 端點" name="apiEndpoint">
              <Input placeholder="https://api.example.com" />
            </Form.Item>
            <Form.Item label="超時時間 (秒)" name="timeout">
              <Input type="number" placeholder="30" />
            </Form.Item>
            <Form.Item label="重試次數" name="retryCount">
              <Input type="number" placeholder="3" />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary">保存</Button>
                <Button>重置</Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      ),
    },
    {
      key: '3',
      label: (
        <span className="config-tab-label">
          安全設定
        </span>
      ),
      children: (
        <div style={{ padding: '1rem' }}>
          <Form layout="vertical">
            <Form.Item label="API 金鑰" name="apiKey">
              <Input.Password placeholder="請輸入 API 金鑰" />
            </Form.Item>
            <Form.Item label="加密方式" name="encryption">
              <Input placeholder="AES-256" />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button type="primary">保存</Button>
                <Button danger>清除</Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      ),
    },
  ]

  return (
    <div style={{ padding: '2rem', background: 'var(--color-gray_4)', minHeight: '100vh' }}>
      <h1 style={{ color: 'var(--color-gray_0)', marginBottom: '2rem' }}>
        ConfigSubTabs 使用示例
      </h1>
      
      <div style={{ marginBottom: '2rem' }}>
        <h2 style={{ color: 'var(--color-gray_1)', fontSize: '1.2rem' }}>
          遷移後的樣式效果
        </h2>
        <p style={{ color: 'var(--color-gray_1)', marginBottom: '1rem' }}>
          此元件已成功從 configLayout.css 遷移到 CSS Modules，
          保持了原有的玻璃擬態效果和動畫。
        </p>
      </div>

      <ConfigSubTabs
        items={tabItems}
        defaultActiveKey="1"
        className="example-tabs"
      />

      <div style={{ marginTop: '2rem', padding: '1rem', background: 'var(--color-gray_3)', borderRadius: '8px' }}>
        <h3 style={{ color: 'var(--color-gray_0)', marginBottom: '1rem' }}>
          遷移特點：
        </h3>
        <ul style={{ color: 'var(--color-gray_1)', lineHeight: '1.6' }}>
          <li>✅ 使用 CSS Modules 的 :global() 語法解決嵌套選擇器問題</li>
          <li>✅ 保持原有的玻璃擬態視覺效果</li>
          <li>✅ 支援響應式設計</li>
          <li>✅ 完整的動畫和過渡效果</li>
          <li>✅ 與 Antd ConfigProvider 主題系統整合</li>
          <li>✅ 類型安全的 TypeScript 支援</li>
        </ul>
      </div>
    </div>
  )
}

export default ConfigSubTabsExample
