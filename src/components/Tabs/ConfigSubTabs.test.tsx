import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'

import ConfigSubTabs from './ConfigSubTabs'

describe('ConfigSubTabs', () => {
  it('renders with correct CSS classes', () => {
    const { container } = render(
      <ConfigSubTabs
        items={[
          {
            key: '1',
            label: 'Tab 1',
            children: <div>Content 1</div>,
          },
          {
            key: '2',
            label: 'Tab 2',
            children: <div>Content 2</div>,
          },
        ]}
      />
    )

    // 檢查是否有正確的 CSS Modules 類名
    const tabsElement = container.querySelector('.ant-tabs')
    expect(tabsElement).toHaveClass(expect.stringMatching(/configSubTabs/))
  })

  it('applies custom className correctly', () => {
    const customClass = 'custom-tabs'
    const { container } = render(
      <ConfigSubTabs
        className={customClass}
        items={[
          {
            key: '1',
            label: 'Tab 1',
            children: <div>Content 1</div>,
          },
        ]}
      />
    )

    const tabsElement = container.querySelector('.ant-tabs')
    expect(tabsElement).toHaveClass(customClass)
  })

  it('renders tab content correctly', () => {
    render(
      <ConfigSubTabs
        items={[
          {
            key: '1',
            label: 'Test Tab',
            children: <div>Test Content</div>,
          },
        ]}
      />
    )

    expect(screen.getByText('Test Tab')).toBeInTheDocument()
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })

  it('supports multiple tabs', () => {
    render(
      <ConfigSubTabs
        items={[
          {
            key: '1',
            label: 'Tab 1',
            children: <div>Content 1</div>,
          },
          {
            key: '2',
            label: 'Tab 2',
            children: <div>Content 2</div>,
          },
          {
            key: '3',
            label: 'Tab 3',
            children: <div>Content 3</div>,
          },
        ]}
      />
    )

    expect(screen.getByText('Tab 1')).toBeInTheDocument()
    expect(screen.getByText('Tab 2')).toBeInTheDocument()
    expect(screen.getByText('Tab 3')).toBeInTheDocument()
  })
})
