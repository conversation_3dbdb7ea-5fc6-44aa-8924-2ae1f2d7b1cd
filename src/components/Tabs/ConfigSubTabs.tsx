import { ConfigProvider, Tabs, type TabsProps } from 'antd'

import { color } from 'src/utils/variables'

interface Props extends TabsProps { }

function ConfigSubTabs({ className, rootClassName, ...tabsProps }: Props) {
  return (
    <ConfigProvider theme={{
      components: {
        Tabs: {
          colorBgContainer: color.primary.default,
          colorPrimaryBorder: color.primary.default,
          colorBorderSecondary: 'transparent',
          cardPadding: '2px 1rem',
          lineHeight: 2,
          margin: 24,
        },
      },
    }}
    >
      <Tabs
        defaultActiveKey="1"
        type="card"
        id="config-sub-tabs"
        tabBarGutter={24}
        className={`${rootClassName || ''} ${className || ''}`}
        {...tabsProps}
      />
    </ConfigProvider>
  )
}

export default ConfigSubTabs
