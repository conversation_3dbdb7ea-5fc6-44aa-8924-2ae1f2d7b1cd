
.configSubTabs :global(.ant-tabs-nav) {
  margin-bottom: 1.5rem;
  /* background: url("https://images.unsplash.com/photo-1588943211346-0908a1fb0b01?crop=entropy&cs=srgb&fm=jpg&ixid=M3wzMjM4NDZ8MHwxfHJhbmRvbXx8fHx8fHx8fDE3NDk1MzU4MDV8&ixlib=rb-4.1.0&q=85") center/cover; */
}

.configSubTabs :global(.ant-tabs-nav::before) {
  border: none;
}

.configSubTabs :global(.ant-tabs-nav-list) {
  position: relative;
  padding: 4px;
  z-index: 1;
  border-radius: 100px;
  box-shadow: 0 6px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1);
}

.configSubTabs :global(.ant-tabs-nav-list::after),
.configSubTabs :global(.ant-tabs-nav-list::before) {
  content: '';
  position: absolute;
  border-radius: 100px;
  width: 100%;
  height: 100%;
  inset: 0;
  left: 0;
  top: 0;
}

.configSubTabs :global(.ant-tabs-nav-list::before) {
  z-index: 5;
  background: var(--lg-bg-color);
  backdrop-filter: blur(4px);
  box-shadow: inset 1px 1px 0 var(--lg-highlight),
    inset 0 0 8px var(--lg-highlight);
}

.configSubTabs :global(.ant-tabs-nav-list::after) {
  z-index: 1;
  filter: url(#lensFilter) saturate(120%) brightness(1.15);
}

/* Tab 樣式 */
.configSubTabs :global(.ant-tabs-tab) {
  position: relative;
  z-index: 5;
  padding: 0 0.5rem;
  border-radius: 100px;
  overflow: hidden;
  transition: transform 200ms;
}

.configSubTabs :global(.ant-tabs-tab .config-tab-label) {
  transform: scale(.95);
  transition: transform 200ms;
}

/* 活躍 Tab 樣式 */
.configSubTabs :global(.ant-tabs-tab.ant-tabs-tab-active) {
  background: rgba(48, 48, 48, 0.4);
  backdrop-filter: blur(2px) saturate(180%);
  border: transparent;
}

.configSubTabs :global(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn > span) {
  color: var(--color-gray_0);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .configSubTabs :global(.ant-tabs-nav) {
    margin-bottom: 1rem;
  }

  .configSubTabs :global(.ant-tabs-nav-list) {
    padding: 2px;
  }

  .configSubTabs :global(.ant-tabs-tab) {
    padding: 0 0.25rem;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .configSubTabs :global(.ant-tabs-nav-list) {
    border-radius: 50px;
  }

  .configSubTabs :global(.ant-tabs-tab) {
    padding: 0 0.125rem;
    font-size: 12px;
  }
}
