/* CSS Modules 解決方案：使用 :global() 語法 */
.configSubTabs {
  /* 容器樣式 */
}

/* 使用 :global() 來定制 Antd 內部元素 */
.configSubTabs :global(.ant-tabs-nav) {
  background: var(--color-primary);
  border-radius: 8px 8px 0 0;
}

.configSubTabs :global(.ant-tabs-tab) {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
}

.configSubTabs :global(.ant-tabs-tab-active) {
  background: var(--color-gray_3);
  color: var(--color-gray_0);
}

.configSubTabs :global(.ant-tabs-tab:hover) {
  color: var(--color-gray_0);
}

.configSubTabs :global(.ant-tabs-content-holder) {
  background: var(--color-gray_3);
  padding: 1rem;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .configSubTabs :global(.ant-tabs-nav) {
    margin: 0;
  }
  
  .configSubTabs :global(.ant-tabs-tab) {
    padding: 8px 12px;
    font-size: 14px;
  }
}
