
/* 使用更高優先級的選擇器來覆蓋 Antd 樣式 */
.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card > .ant-tabs-nav) {
  margin-bottom: 1.5rem !important;
  /* background: url("https://images.unsplash.com/photo-1588943211346-0908a1fb0b01?crop=entropy&cs=srgb&fm=jpg&ixid=M3wzMjM4NDZ8MHwxfHJhbmRvbXx8fHx8fHx8fDE3NDk1MzU4MDV8&ixlib=rb-4.1.0&q=85") center/cover; */
}

.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card > .ant-tabs-nav::before) {
  border: none !important;
}

.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-nav-list) {
  position: relative !important;
  padding: 4px !important;
  z-index: 1 !important;
  border-radius: 100px !important;
  box-shadow: 0 6px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1) !important;
}

.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-nav-list::after),
.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-nav-list::before) {
  content: '' !important;
  position: absolute !important;
  border-radius: 100px !important;
  width: 100% !important;
  height: 100% !important;
  inset: 0 !important;
  left: 0 !important;
  top: 0 !important;
}

.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-nav-list::before) {
  z-index: 5 !important;
  background: var(--lg-bg-color) !important;
  backdrop-filter: blur(4px) !important;
  box-shadow: inset 1px 1px 0 var(--lg-highlight),
    inset 0 0 8px var(--lg-highlight) !important;
}

.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-nav-list::after) {
  z-index: 1 !important;
  filter: url(#lensFilter) saturate(120%) brightness(1.15) !important;
}

/* Tab 樣式 - 使用更高優先級 */
.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-tab) {
  position: relative !important;
  z-index: 5 !important;
  padding: 0 0.5rem !important;
  border-radius: 100px !important;
  overflow: hidden !important;
  transition: transform 200ms !important;
  background: transparent !important;
  border: transparent !important;
}

.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-tab .config-tab-label) {
  transform: scale(.95) !important;
  transition: transform 200ms !important;
}

/* 活躍 Tab 樣式 - 使用更高優先級覆蓋 Antd */
.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-tab.ant-tabs-tab-active) {
  background: rgba(48, 48, 48, 0.4) !important;
  backdrop-filter: blur(2px) saturate(180%) !important;
  border: transparent !important;
}

.configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn > span) {
  color: var(--color-gray_0) !important;
}

/* 響應式設計 - 使用更高優先級 */
@media (max-width: 768px) {
  .configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card > .ant-tabs-nav) {
    margin-bottom: 1rem !important;
  }

  .configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-nav-list) {
    padding: 2px !important;
  }

  .configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-tab) {
    padding: 0 0.25rem !important;
    font-size: 14px !important;
  }
}

@media (max-width: 480px) {
  .configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-nav-list) {
    border-radius: 50px !important;
  }

  .configSubTabs.configSubTabs :global(.ant-tabs.ant-tabs-top.ant-tabs-card .ant-tabs-tab) {
    padding: 0 0.125rem !important;
    font-size: 12px !important;
  }
}
