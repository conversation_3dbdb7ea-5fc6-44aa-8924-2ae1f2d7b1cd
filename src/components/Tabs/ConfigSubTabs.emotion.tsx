import { ConfigProvider, Tabs, type TabsProps } from 'antd'
import styled from '@emotion/styled'
import { css } from '@emotion/react'

import { color } from 'src/utils/variables'

interface Props extends TabsProps { }

// Emotion styled component 解決方案
const StyledTabsContainer = styled.div`
  .ant-tabs-nav {
    background: ${color.primary.default};
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
  }

  .ant-tabs-tab {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;

    &:hover {
      color: ${color.gray[0].default};
    }

    &.ant-tabs-tab-active {
      background: ${color.gray[3]};
      color: ${color.gray[0].default};
      
      .ant-tabs-tab-btn {
        color: ${color.gray[0].default};
      }
    }
  }

  .ant-tabs-content-holder {
    background: ${color.gray[3]};
    padding: 1rem;
    border-radius: 0 0 8px 8px;
  }

  .ant-tabs-tabpane {
    outline: none;
  }

  /* 響應式設計 */
  @media (max-width: 768px) {
    .ant-tabs-nav {
      margin: 0;
    }
    
    .ant-tabs-tab {
      padding: 8px 12px;
      font-size: 14px;
    }
  }
`

// 使用 css prop 的替代方案
const tabsStyles = css`
  .ant-tabs-nav {
    background: ${color.primary.default};
    border-radius: 8px 8px 0 0;
  }

  .ant-tabs-tab {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);

    &:hover {
      color: ${color.gray[0].default};
    }

    &.ant-tabs-tab-active {
      background: ${color.gray[3]};
      color: ${color.gray[0].default};
    }
  }

  .ant-tabs-content-holder {
    background: ${color.gray[3]};
    padding: 1rem;
  }
`

function ConfigSubTabsEmotion({ className, rootClassName, ...tabsProps }: Props) {
  return (
    <ConfigProvider theme={{
      components: {
        Tabs: {
          colorBgContainer: color.primary.default,
          colorPrimaryBorder: color.primary.default,
          colorBorderSecondary: 'transparent',
          cardPadding: '2px 1rem',
          lineHeight: 2,
          margin: 24,
        },
      },
    }}
    >
      <StyledTabsContainer>
        <Tabs
          defaultActiveKey="1"
          type="card"
          id="config-sub-tabs"
          tabBarGutter={24}
          className={`${rootClassName || ''} ${className || ''}`}
          {...tabsProps}
        />
      </StyledTabsContainer>
    </ConfigProvider>
  )
}

// 使用 css prop 的版本
export function ConfigSubTabsWithCssProp({ className, rootClassName, ...tabsProps }: Props) {
  return (
    <ConfigProvider theme={{
      components: {
        Tabs: {
          colorBgContainer: color.primary.default,
          colorPrimaryBorder: color.primary.default,
          colorBorderSecondary: 'transparent',
          cardPadding: '2px 1rem',
          lineHeight: 2,
          margin: 24,
        },
      },
    }}
    >
      <div css={tabsStyles}>
        <Tabs
          defaultActiveKey="1"
          type="card"
          id="config-sub-tabs"
          tabBarGutter={24}
          className={`${rootClassName || ''} ${className || ''}`}
          {...tabsProps}
        />
      </div>
    </ConfigProvider>
  )
}

export default ConfigSubTabsEmotion
