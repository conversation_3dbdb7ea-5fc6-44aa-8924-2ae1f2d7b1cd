import { ConfigProvider, Tabs, type TabsProps } from 'antd'
import { css } from '@emotion/react'

import { color } from 'src/utils/variables'

interface Props extends TabsProps { 
  variant?: 'default' | 'primary' | 'secondary'
}

// 主題變體定義
const getTabsTheme = (variant: Props['variant'] = 'default') => {
  const themes = {
    default: {
      colorBgContainer: color.primary.default,
      colorPrimaryBorder: color.primary.default,
      colorBorderSecondary: 'transparent',
      cardPadding: '2px 1rem',
      lineHeight: 2,
      margin: 24,
    },
    primary: {
      colorBgContainer: color.primary.dark,
      colorPrimaryBorder: color.primary.dark,
      colorBorderSecondary: 'transparent',
      cardPadding: '4px 1.5rem',
      lineHeight: 2.2,
      margin: 16,
    },
    secondary: {
      colorBgContainer: color.gray[2],
      colorPrimaryBorder: color.gray[1].default,
      colorBorderSecondary: 'transparent',
      cardPadding: '2px 0.75rem',
      lineHeight: 1.8,
      margin: 12,
    }
  }
  
  return themes[variant]
}

// 動態樣式生成
const getTabsStyles = (variant: Props['variant'] = 'default') => css`
  .ant-tabs-nav {
    background: ${variant === 'primary' ? color.primary.dark : color.primary.default};
    border-radius: 8px 8px 0 0;
    margin-bottom: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .ant-tabs-tab {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background: ${color.gray[0].default};
      transition: all 0.3s ease;
      transform: translateX(-50%);
    }

    &:hover {
      color: ${color.gray[0].default};
      background: rgba(255, 255, 255, 0.1);
      
      &::before {
        width: 80%;
      }
    }

    &.ant-tabs-tab-active {
      background: ${color.gray[3]};
      color: ${color.gray[0].default};
      
      &::before {
        width: 100%;
        background: ${color.primary.light};
      }
      
      .ant-tabs-tab-btn {
        color: ${color.gray[0].default};
        font-weight: 600;
      }
    }
  }

  .ant-tabs-content-holder {
    background: ${color.gray[3]};
    padding: ${variant === 'primary' ? '1.5rem' : '1rem'};
    border-radius: 0 0 8px 8px;
    min-height: ${variant === 'primary' ? '400px' : '300px'};
  }

  .ant-tabs-tabpane {
    outline: none;
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 響應式設計 */
  @media (max-width: 768px) {
    .ant-tabs-nav {
      margin: 0;
    }
    
    .ant-tabs-tab {
      padding: 8px 12px;
      font-size: 14px;
      
      &::before {
        display: none;
      }
    }

    .ant-tabs-content-holder {
      padding: 0.75rem;
      min-height: 250px;
    }
  }

  @media (max-width: 480px) {
    .ant-tabs-tab {
      padding: 6px 8px;
      font-size: 12px;
    }
  }
`

function ConfigSubTabsHybrid({ 
  className, 
  rootClassName, 
  variant = 'default',
  ...tabsProps 
}: Props) {
  const theme = getTabsTheme(variant)
  const styles = getTabsStyles(variant)

  return (
    <ConfigProvider theme={{
      components: {
        Tabs: theme,
      },
    }}
    >
      <div css={styles}>
        <Tabs
          defaultActiveKey="1"
          type="card"
          id="config-sub-tabs"
          tabBarGutter={variant === 'primary' ? 32 : 24}
          className={`config-sub-tabs-${variant} ${rootClassName || ''} ${className || ''}`}
          {...tabsProps}
        />
      </div>
    </ConfigProvider>
  )
}

export default ConfigSubTabsHybrid
