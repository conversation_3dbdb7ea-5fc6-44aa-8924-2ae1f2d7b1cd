import React from 'react'
import { Button, Checkbox, Form, Input, Select, TreeSelect } from 'antd'

// 引入各個模組的樣式
import formStyles from '@/styles/components/form/config-form.module.css'
import structureStyles from '@/styles/components/structure/edit-structure.module.css'
import tagStyles from '@/styles/components/structure/structure-tag.module.css'
import selectStyles from '@/styles/components/select/filter-select.module.css'

interface Props {
  // 元件 props 定義
}

/**
 * 配置佈局示例元件
 * 展示如何使用重構後的 CSS Modules
 */
function ConfigLayoutExample(props: Props) {
  return (
    <div className={structureStyles.editStructure}>
      {/* 導航欄區域 */}
      <nav className={structureStyles.editStructureNavbar}>
        <h2>結構編輯器</h2>
        
        {/* 過濾選擇器 */}
        <div className={selectStyles.filterSelect}>
          <TreeSelect
            placeholder="選擇過濾條件"
            treeData={[
              { title: '全部', value: 'all' },
              { title: '已選擇', value: 'selected' },
            ]}
            className="filter-select"
            dropdownClassName={selectStyles.filterSelectDropdown}
          />
        </div>
        
        {/* 計數球 */}
        <span className={tagStyles.checkCountBall}>5</span>
      </nav>

      {/* 主要內容區域 */}
      <main className={structureStyles.editStructureMain}>
        {/* 表單區域 */}
        <div className={formStyles.formItemControl}>
          <Form layout="vertical">
            <Form.Item label="結構名稱" name="name">
              <Input placeholder="請輸入結構名稱" />
            </Form.Item>
            
            {/* 無邊框複選框 */}
            <div className={formStyles.noCheckboxBorder}>
              <Checkbox>啟用此結構</Checkbox>
            </div>
          </Form>
        </div>

        {/* 結構標籤區域 */}
        <div className="structure-tags">
          <div className={`${tagStyles.structureTag} structure-tag`}>
            <span>結構標籤 1</span>
            <span className={`${tagStyles.structureTagIcon} ${tagStyles.tagCheckable}`} />
          </div>
          
          <div className={`${tagStyles.structureTag} structure-tag tag-checkable`}>
            <span>結構標籤 2</span>
            <span className={`${tagStyles.structureTagIcon} ${tagStyles.tagCheckable}`} />
          </div>
        </div>

        {/* 過濾選單 */}
        <div className={structureStyles.editStructureFilterMenu}>
          <ul>
            <li>
              <Input placeholder="搜尋..." />
            </li>
          </ul>
        </div>
      </main>
    </div>
  )
}

export default ConfigLayoutExample
