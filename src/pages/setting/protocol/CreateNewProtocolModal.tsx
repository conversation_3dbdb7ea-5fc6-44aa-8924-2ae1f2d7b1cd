import { useNavigate } from 'react-router'

import { GenericTwoStepModal, type FirstStepConfig, type SecondStepConfig } from '@/layouts'
import { useAppSelector } from '@/store/hook'
import { withoutTime } from '@/utils/helper'
import { ModalHooksReturn } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useCheckProtocolNameMutation } from 'src/services/api'

type Props = Omit<ModalHooksReturn<unknown>, 'triggerProps'>

function CreateNewProtocolModal({ ...modalProps }: Props) {
  // router
  const navigate = useNavigate()

  // redux
  const { protocols } = useAppSelector((state) => state.protocolReducer)
  const [checkProtocolNameMutation] = useCheckProtocolNameMutation()

  // Configuration for step
  const firstStepConfig: FirstStepConfig = {
    label: i18n.t('titles.create_item_name', {
      name: i18n.t('titles.protocol'),
      joinArrays: ' ',
    }),
    placeholder: i18n.t('form_placeholders.enter_variable', {
      variable: i18n.t('titles.protocol_name'),
      joinArrays: ' ',
    }),
    maxLength: 16,
  }

  const secondStepConfig: SecondStepConfig<ProtocolType> = {
    title: 'Select Protocol Template',
    description: i18n.t('paragraphs.create_new_modal.third', {
      name: i18n.t('titles.protocol').toLocaleLowerCase(),
      joinArrays: ' ',
    }),
    createNewOptionText: i18n.t('paragraphs.create_new_modal.first', {
      name: i18n.t('titles.protocol').toLocaleLowerCase(),
      joinArrays: ' ',
    }),
    errorMessage: i18n.t('error_contents.choose_protocol', {
      item: i18n.t('titles.protocols').toLocaleLowerCase(),
      joinArrays: ' ',
    }),
    tableProps: {
      dataSource: protocols,
      columns: [
        {
          title: i18n.t('titles.protocol_name'),
          dataIndex: 'protocol_name',
          key: 'protocol_name',
          width: '160px',
        },
        {
          title: i18n.t('titles.last_modified'),
          dataIndex: 'last_mod_time',
          key: 'last_mod_time',
          width: '180px',
          render(_, { last_mod_time }) {
            return withoutTime(last_mod_time)
          },
        },
        {
          title: i18n.t('titles.description'),
          dataIndex: 'description',
          key: 'description',
          width: '300px',
        },
      ],
    },
  }

  // API and business logic handlers
  const handleFirstStepSubmit = async (protocolName: string) => {
    await checkProtocolNameMutation({ name: protocolName }).unwrap()
  }

  const handleSecondStepSubmit = async (selectedId: string | number, protocolName: string) => {
    navigate('create', { state: { protocolName, copyProtocolID: selectedId } })
  }

  // Error handling
  const handleError = (error: unknown) => {
    if (typeof error === 'object' && error !== null) {
      if ('status' in error) {
        const statusError = error as { status: number }
        return statusError.status === 409
          ? i18n.t('form_rules.variable_unique', {
            variable: i18n.t('titles.protocol_name'),
            joinArrays: ' ',
          })
          : i18n.t('error_contents.server_error')
      }
    }
    return i18n.t('form_rules.enter_variable', {
      variable: i18n.t('titles.protocol_name'),
      joinArrays: ' ',
    })
  }

  return (
    <GenericTwoStepModal<ProtocolType>
      {...modalProps}
      firstStepConfig={firstStepConfig}
      secondStepConfig={secondStepConfig}
      onFirstStepSubmit={handleFirstStepSubmit}
      onSecondStepSubmit={handleSecondStepSubmit}
      onError={handleError}
    />
  )
}

export default CreateNewProtocolModal
