import type { ColumnsType } from 'antd/es/table/interface'

import i18n from 'src/i18n'
import { color } from 'src/utils/variables'

// 要改 type
export const historyColumns: ColumnsType<any> = [
  {
    title: i18n.t('titles.patient_id'),
    dataIndex: 'patient_id',
    key: 'patient_id',
    width: '100px',
    render: (id) => <div style={{ paddingLeft: '16px' }}>{id}</div>,
    sorter: true,
    // sortOrder: sortedInfo.columnKey === 'patient_id' ? sortedInfo.order : null,
    showSorterTooltip: false,
    ellipsis: true,
  },
  {
    title: i18n.t('titles.patient_name'),
    dataIndex: 'patient_name',
    key: 'patient_name',
    width: '100px',
    sorter: true,
    // sortOrder: sortedInfo.columnKey === 'patient_name' ? sortedInfo.order : null,
    showSorterTooltip: false,
    render: (_, { patient_name }) => (
      <p style={{
        minWidth: 90,
        color: color.gray[1].default,
      }}
      >
        {patient_name}
      </p>
    ),
  },
  {
    title: i18n.t('titles.last_modified'),
    dataIndex: 'last_mod_time',
    key: 'last_mod_time',
    width: '100px',
    sorter: true,
    // sortOrder: sortedInfo.columnKey === 'study_status' ? sortedInfo.order : null,
    showSorterTooltip: false,
    render: (_, { last_mod_time }) => (
      <p style={{
        minWidth: 90,
        color: color.gray[1].default,
      }}
      >
        {last_mod_time}
      </p>
    ),
  },
]
