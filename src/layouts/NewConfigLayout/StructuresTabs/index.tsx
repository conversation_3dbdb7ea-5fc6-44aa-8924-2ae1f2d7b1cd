import { useState } from 'react'

import { ConfigProvider, type TabsProps } from 'antd'

import ConfigTabItemLabel from '@/components/Tabs/ConfigTabItemLabel'
import ConfigSubTabs from 'src/components/Tabs/ConfigSubTabs'
import useSortStructure from 'src/hooks/useSortStructure'
import i18n from 'src/i18n'
import { useAppDispatch, useAppSelector } from 'src/store/hook'
import { updateStructureSort } from 'src/store/reducers/detailSlice'
import { color } from 'src/utils/variables'

import ProcessTable from './ProcessTable'
import SelectStructureTabs from './SelectStructureTabs'
import SortStructuresTable, { type SortableDataType } from './SortStructuresTable'

type SubTabItems = 'select' | 'process' | 'sort'

function StructuresTabs() {
  // state
  const [selectedTabItem, setSelectedTabItem] = useState<SubTabItems>('select')
  const [sortableDataSource, setSortableDataSource] = useState<SortableDataType[]>([])

  // redux
  const { configRequired } = useAppSelector((state) => state.configReducer)
  const dispatch = useAppDispatch()

  // hook
  const { updateSort } = useSortStructure()

  // handlers
  const handleUpdateSortable = (data: SortableDataType[]) => {
    const mergedSort = data.map(({ id, sort, mode }) => ({
      id,
      sort,
      customized: mode !== undefined,
    }))
    dispatch(updateStructureSort(mergedSort))
    setSortableDataSource(data)
  }

  const handleSortingStructures = async () => {
    const { dataSource } = await updateSort()
    setSortableDataSource(dataSource as SortableDataType[])
  }

  // data
  const tabItems: TabsProps['items'] = [
    {
      key: 'select',
      label: (
        <ConfigTabItemLabel
          style={{ color: color.gray[0].default }}
          warn={configRequired.structures}
          warnClassName="right-move"
        >
          {i18n.t('buttons.select_structures')}
        </ConfigTabItemLabel>),
      children: (
        <SelectStructureTabs />
      ),
    },
    {
      key: 'process',
      label: (
        <ConfigTabItemLabel
          style={{ color: color.gray[0].default }}
          warn={!!configRequired.customized_structures.length}
          warnClassName="right-move"
        >
          {i18n.t('buttons.process_structures')}
        </ConfigTabItemLabel>),
      children: (<ProcessTable />),
    },
    {
      key: 'sort',
      label: (
        <ConfigTabItemLabel style={{ color: color.gray[0].default }}>
          {i18n.t('buttons.sort_structures')}
        </ConfigTabItemLabel>
      ),
      children: (<SortStructuresTable
        dataSource={sortableDataSource}
        onDragChange={handleUpdateSortable}
      />),
    },
  ]

  return (
    <ConfigProvider theme={{
      components: {
        Table: {
          headerBg: color.gray[3],
          colorBgContainer: color.gray[3],
        },
      },
    }}
    >
      <ConfigSubTabs
        activeKey={selectedTabItem}
        items={tabItems}
        onChange={async (value) => {
          if (value === 'sort') await handleSortingStructures()
          setSelectedTabItem(value as SubTabItems)
        }}
      />
    </ConfigProvider>
  )
}

export default StructuresTabs
